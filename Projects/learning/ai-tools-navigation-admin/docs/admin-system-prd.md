# AI导航站后台管理系统需求文档

## 一、项目概述

### 1.1 项目背景
基于现有的AI导航站（使用Supabase作为后端），需要开发一个后台管理系统，用于管理AI工具、审核用户提交、以及用户权限管理。

### 1.2 目标用户
- 网站管理员
- 具有管理权限的用户

### 1.3 技术栈
- 前端：React + TypeScript + Ant Design / Material-UI
- 后端：Supabase（PostgreSQL + Auth + API）
- 状态管理：React Query / SWR
- 路由：React Router

---

## 二、数据库设计扩展

### 2.1 新增表：admin_users（管理员用户表）

**用途**：管理哪些用户具有后台管理权限

| 字段名     | 类型                     | 约束                                          | 描述                 |
| ---------- | ------------------------ | --------------------------------------------- | -------------------- |
| id         | UUID                     | PRIMARY KEY, DEFAULT gen_random_uuid()        | 记录唯一标识         |
| user_id    | UUID                     | NOT NULL, REFERENCES auth.users(id) UNIQUE    | 用户ID（关联Supabase Auth） |
| role       | VARCHAR(20)              | NOT NULL, DEFAULT 'admin'                     | 角色：admin/super_admin |
| is_active  | BOOLEAN                  | NOT NULL, DEFAULT TRUE                        | 是否激活             |
| created_by | UUID                     | REFERENCES auth.users(id) ON DELETE SET NULL  | 创建者               |
| created_at | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                       | 创建时间             |
| updated_at | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT now()                       | 最后更新时间         |

**建表SQL**：
```sql
-- 创建管理员用户表
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL DEFAULT 'admin',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 启用行级安全策略
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 只允许管理员查看管理员列表
CREATE POLICY "admin_read_admin_users"
  ON admin_users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员添加新管理员
CREATE POLICY "admin_insert_admin_users"
  ON admin_users
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员移除管理员
CREATE POLICY "admin_delete_admin_users"
  ON admin_users
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员更新管理员信息（如修改角色、启用/禁用状态）
CREATE POLICY "admin_update_admin_users"
  ON admin_users
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 创建索引
CREATE INDEX idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX idx_admin_users_is_active ON admin_users(is_active);
```

---

## 三、功能需求详细说明

### 3.1 权限控制系统

#### 3.1.1 登录验证
- **需求**：只有在admin_users表中且is_active=true的用户才能访问后台
- **实现逻辑**：
  1. 用户通过Supabase Auth登录
  2. 检查当前用户ID是否存在于admin_users表中
  3. 检查is_active字段是否为true
  4. 验证通过才能进入后台系统

#### 3.1.2 路由保护
- 所有后台路由都需要管理员权限验证
- 非管理员用户访问时重定向到登录页面或显示无权限提示

### 3.2 侧边栏导航

#### 3.2.1 导航结构
```
后台管理系统
├── 概览 (Overview)
├── 审核 (Review)
└── 用户管理 (User Management)
```

---

## 四、页面功能详细设计

### 4.1 概览页面 (Overview)

#### 4.1.1 页面布局
```
[顶部操作栏]
┌─────────────────────────────────────────────────────┐
│  概览                                    [+ 添加AI工具] │
└─────────────────────────────────────────────────────┘

[统计卡片区域]
┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐
│AI工具数量│ │AI分类数量│ │今日提交 │ │待审核   │
│   123   │ │   15    │ │   5     │ │   8     │
└─────────┘ └─────────┘ └─────────┘ └─────────┘

[AI工具列表]
┌─────────────────────────────────────────────────────┐
│  所有AI工具                                          │
│  ┌─────┬──────┬──────┬──────┬──────┬──────┐        │
│  │Logo │ 名称 │ 分类 │ 状态 │ 时间 │ 操作 │        │
│  └─────┴──────┴──────┴──────┴──────┴──────┘        │
└─────────────────────────────────────────────────────┘
```

#### 4.1.2 统计卡片数据源
1. **AI工具数量**
   - 数据源：`SELECT COUNT(*) FROM tools WHERE status = 'published'`
   - 显示：当前已发布的工具总数

2. **AI分类数量**
   - 数据源：`SELECT COUNT(*) FROM categories`
   - 显示：分类总数

3. **今日提交**
   - 数据源：`SELECT COUNT(*) FROM tool_submissions WHERE DATE(submitted_at) = CURRENT_DATE`
   - 显示：今天用户提交的工具数量

4. **待审核**
   - 数据源：`SELECT COUNT(*) FROM tool_submissions WHERE status = 'pending'`
   - 显示：未审核的提交数量

#### 4.1.3 添加AI工具功能
- **触发方式**：点击右上角"+ 添加AI工具"按钮
- **交互形式**：右侧弹出Drawer抽屉组件
- **表单字段**：
  ```
  工具名称 * (name)
  工具网址 * (url)  
  工具分类 * (category_id) - 下拉选择
  Logo地址 (logo_url)
  封面图地址 (cover_url)
  简要描述 (summary) - 文本域
  详细描述 (description) - 富文本编辑器/Markdown编辑器
  常见问题 (faqs) - 富文本编辑器/Markdown编辑器
  是否热门 (is_popular) - 开关
  是否新工具 (is_new) - 开关
  ```
- **提交逻辑**：直接插入到tools表中，状态为'published'

#### 4.1.4 AI工具列表
- **数据源**：`SELECT * FROM tools ORDER BY created_at DESC`
- **列表字段**：
  - Logo缩略图
  - 工具名称
  - 所属分类
  - 状态（published/archived）
  - 创建时间
  - 操作按钮（查看/编辑/删除）
- **分页**：支持分页加载，每页20条
- **搜索**：支持按名称搜索
- **筛选**：支持按分类、状态筛选

### 4.2 审核页面 (Review)

#### 4.2.1 页面布局
```
[页面标题]
┌─────────────────────────────────────────────────────┐
│  待审核提交                                          │
└─────────────────────────────────────────────────────┘

[筛选器]
┌─────────────────────────────────────────────────────┐
│ 状态: [全部▼] 分类: [全部▼] 时间: [最近7天▼]        │
└─────────────────────────────────────────────────────┘

[提交列表]
┌─────────────────────────────────────────────────────┐
│  ┌─────┬──────┬──────┬──────┬──────┬──────┐        │
│  │Logo │ 名称 │ 分类 │ 状态 │ 提交者│ 操作 │        │
│  └─────┴──────┴──────┴──────┴──────┴──────┘        │
└─────────────────────────────────────────────────────┘
```

#### 4.2.2 数据源与筛选
- **主要数据源**：
  ```sql
  SELECT ts.*, c.name as category_name, u.email as submitter_email
  FROM tool_submissions ts
  LEFT JOIN categories c ON ts.category_id = c.id
  LEFT JOIN auth.users u ON ts.user_id = u.id
  WHERE ts.status = 'pending'
  ORDER BY ts.submitted_at DESC
  ```

#### 4.2.3 审核操作
- **查看详情**：点击查看按钮，弹出模态框显示完整提交信息
- **审核通过**：
  1. 将tool_submissions中的status改为'approved'
  2. 设置reviewed_by为当前管理员ID
  3. 设置reviewed_at为当前时间
  4. 将数据复制到tools表中
  5. 发送通知给提交者（可选）
- **审核拒绝**：
  1. 将status改为'rejected'
  2. 填写拒绝原因到review_comments
  3. 设置审核人和审核时间

### 4.3 用户管理页面 (User Management)

#### 4.3.1 页面布局
```
[页面标题与操作]
┌─────────────────────────────────────────────────────┐
│  用户管理                              [+ 添加管理员] │
└─────────────────────────────────────────────────────┘

[管理员列表]
┌─────────────────────────────────────────────────────┐
│  ┌──────┬──────┬──────┬──────┬──────┬──────┐        │
│  │ 头像 │ 邮箱 │ 角色 │ 状态 │ 时间 │ 操作 │        │
│  └──────┴──────┴──────┴──────┴──────┴──────┘        │
└─────────────────────────────────────────────────────┘
```

#### 4.3.2 功能说明
- **查看管理员列表**：显示所有admin_users中的用户
- **添加管理员**：通过邮箱搜索用户并添加到admin_users表
- **修改权限**：修改用户的role字段（admin/super_admin）
- **启用/禁用**：修改is_active字段
- **删除管理员**：从admin_users表中删除记录

---

## 五、技术实现要点

### 5.1 权限验证Hook
```typescript
// useAdminAuth.ts
const useAdminAuth = () => {
  const { user } = useAuth();
  const { data: isAdmin, isLoading } = useQuery(
    ['admin-check', user?.id],
    () => checkAdminStatus(user?.id),
    { enabled: !!user }
  );
  
  return { isAdmin, isLoading, user };
};
```

### 5.2 数据获取函数
```typescript
// 获取统计数据
const getOverviewStats = async () => {
  const [toolsCount, categoriesCount, todaySubmissions, pendingReviews] = 
    await Promise.all([
      supabase.from('tools').select('*', { count: 'exact', head: true }),
      supabase.from('categories').select('*', { count: 'exact', head: true }),
      supabase.from('tool_submissions')
        .select('*', { count: 'exact', head: true })
        .gte('submitted_at', new Date().toISOString().split('T')[0]),
      supabase.from('tool_submissions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
    ]);
  
  return {
    toolsCount: toolsCount.count,
    categoriesCount: categoriesCount.count,
    todaySubmissions: todaySubmissions.count,
    pendingReviews: pendingReviews.count
  };
};
```

### 5.3 审核流程函数
```typescript
// 审核通过
const approveSubmission = async (submissionId: string) => {
  const { data: submission } = await supabase
    .from('tool_submissions')
    .select('*')
    .eq('id', submissionId)
    .single();
  
  // 开始事务
  const { error } = await supabase.rpc('approve_tool_submission', {
    submission_id: submissionId,
    reviewer_id: currentUser.id
  });
  
  if (error) throw error;
};
```

---

## 六、开发优先级

### Phase 1 - 核心功能（第1-2周）
1. 权限验证系统
2. 概览页面统计卡片
3. AI工具列表展示

### Phase 2 - 管理功能（第3-4周）
1. 添加AI工具功能
2. 审核页面基础功能
3. 审核通过/拒绝逻辑

### Phase 3 - 完善功能（第5-6周）
1. 用户管理页面
2. 搜索和筛选功能
3. 批量操作功能

### Phase 4 - 优化提升（第7-8周）
1. 界面优化和响应式设计
2. 性能优化
3. 错误处理和用户体验提升

---

## 七、注意事项

1. **数据安全**：所有管理操作都需要验证管理员权限
2. **操作日志**：重要操作需要记录日志（可考虑后续扩展）
3. **数据备份**：删除操作需要二次确认
4. **性能考虑**：大量数据时需要分页和虚拟滚动
5. **移动端适配**：考虑响应式设计，支持平板设备使用

---

## 八、后续扩展功能

1. **操作日志系统**：记录所有管理员操作
2. **数据统计分析**：更详细的数据分析和图表
3. **批量操作**：批量审核、批量修改等
4. **通知系统**：审核结果通知用户
5. **内容管理**：网站公告、帮助文档管理
